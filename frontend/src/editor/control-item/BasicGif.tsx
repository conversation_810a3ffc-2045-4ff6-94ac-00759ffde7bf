import BorderAllIcon from "@mui/icons-material/BorderAll";
import CancelIcon from "@mui/icons-material/Cancel";
import CropIcon from "@mui/icons-material/Crop";
import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Typography,
  Slider,
  FormControlLabel,
  Switch,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  MenuItem,
  Select,
  Stack,
  Tab,
  Tabs,
  Tooltip,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { observer } from "mobx-react";
import { StoreContext } from "../../store";
import { GifEditorElement, BorderStyle } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";
import SliderWithInput from "./SliderWithInput";
import FilterControl from "./FilterControl";
import ColorPicker from "../components/color/ColorPicker";
import BaseSetting from "./BaseSetting";

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.8rem",
  fontWeight: theme.typography.fontWeightRegular,

  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

export const BasicGif = observer(() => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();
  const [selectedElement, setSelectedElement] =
    useState<GifEditorElement | null>(null);
  const [isCropping, setIsCropping] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [showBorderSettings, setShowBorderSettings] = useState(false);

  useEffect(() => {
    const element = store.selectedElement;
    if (element && element.type === "gif") {
      setSelectedElement(element as GifEditorElement);
    } else {
      setSelectedElement(null);
    }
  }, [store.selectedElement]);

  if (!selectedElement) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t("select_gif_element")}
        </Typography>
      </Box>
    );
  }

  const handleCrop = () => {
    if (!isCropping) {
      store.startCropMode(selectedElement.id);
      setIsCropping(true);
    } else {
      store.applyCrop();
      setIsCropping(false);
    }
  };

  const handleCancelCrop = () => {
    store.cancelCrop();
    setIsCropping(false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleBorderToggle = () => {
    setShowBorderSettings(!showBorderSettings);
  };

  const updateBorder = (
    property: keyof BorderStyle,
    value: string | number
  ) => {
    store.setMediaElementBorder(selectedElement.id, property, value);
  };

  const handlePropertyChange = (property: string, value: any) => {
    if (selectedElement) {
      const updatedElement = {
        ...selectedElement,
        properties: {
          ...selectedElement.properties,
          [property]: value,
        },
      };
      store.updateEditorElement(updatedElement);
    }
  };

  const handlePlacementChange = (property: string, value: number) => {
    if (selectedElement) {
      const updatedElement = {
        ...selectedElement,
        placement: {
          ...selectedElement.placement,
          [property]: value,
        },
      };
      store.updateEditorElement(updatedElement);
    }
  };

  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          {t("gif_settings")}
        </Typography>
      </Box>
      <Divider />

      <StyledTabs value={activeTab} onChange={handleTabChange}>
        <StyledTab label={t("basic")} />
        <StyledTab label={t("advanced")} />
      </StyledTabs>

      <Box
        sx={{
          ml: 2,
          my: 2,
          mr: 0,
          height: "100%",
          overflowY: "auto",
          overflowX: "hidden",
          pr: 2,

          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-track": {
            bgcolor: "grey.100",
          },
          "&::-webkit-scrollbar-thumb": {
            bgcolor: "grey.400",
            borderRadius: "6px",
          },
        }}
      >
        {activeTab === 0 && (
          <Box>
            <Stack direction="row" spacing={2} sx={{ mb: 1 }}>
              <Tooltip title={isCropping ? t("apply_crop") : t("crop_gif")}>
                <IconButton
                  onClick={handleCrop}
                  color={isCropping ? "secondary" : "primary"}
                >
                  <CropIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              {isCropping && (
                <Tooltip title={t("cancel_crop")}>
                  <IconButton onClick={handleCancelCrop}>
                    <CancelIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip
                title={
                  showBorderSettings ? t("hide_settings") : t("show_settings")
                }
              >
                <IconButton
                  onClick={handleBorderToggle}
                  color={showBorderSettings ? "secondary" : "primary"}
                >
                  <BorderAllIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            {showBorderSettings && (
              <Box sx={{ mb: 3 }}>
                <Stack spacing={2}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px", color: "text.secondary" }}
                    >
                      {t("border_width")}
                    </Typography>
                    <Slider
                      size="small"
                      value={selectedElement.properties.border?.width ?? 0}
                      min={0}
                      max={20}
                      onChange={(e, newValue) =>
                        updateBorder("width", Number(newValue))
                      }
                      sx={{ flex: 1 }}
                    />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px", color: "text.secondary" }}
                    >
                      {t("border_color")}
                    </Typography>
                    <ColorPicker
                      color={
                        selectedElement.properties.border?.color ?? "#000000"
                      }
                      onChange={(color) => updateBorder("color", color)}
                    />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px", color: "text.secondary" }}
                    >
                      {t("border_style")}
                    </Typography>
                    <Select
                      size="small"
                      value={
                        selectedElement.properties.border?.style ?? "solid"
                      }
                      onChange={(e) => updateBorder("style", e.target.value)}
                      sx={{
                        height: "32px",
                        maxWidth: "140px",
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 1,
                        },
                        "& .MuiSelect-select": {
                          padding: "4px 8px",
                          lineHeight: "1.2",
                        },
                      }}
                      renderValue={(selected) => (
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 15,
                              height: 12,
                              mr: 1,
                              border: `2px ${selected} black`,
                            }}
                          />
                          <Typography variant="body2">
                            {t(selected as string)}
                          </Typography>
                        </Box>
                      )}
                    >
                      <MenuItem value="solid">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: "2px solid black",
                            }}
                          />
                          <Typography variant="body2">{t("solid")}</Typography>
                        </Box>
                      </MenuItem>
                      <MenuItem value="dashed">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: "2px dashed black",
                            }}
                          />
                          <Typography variant="body2">{t("dashed")}</Typography>
                        </Box>
                      </MenuItem>
                      <MenuItem value="dotted">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: "2px dotted black",
                            }}
                          />
                          <Typography variant="body2">{t("dotted")}</Typography>
                        </Box>
                      </MenuItem>
                    </Select>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px", color: "text.secondary" }}
                    >
                      {t("border_radius")}
                    </Typography>
                    <Slider
                      size="small"
                      value={
                        selectedElement.properties.border?.borderRadius ?? 0
                      }
                      min={0}
                      max={100}
                      onChange={(e, newValue) =>
                        updateBorder("borderRadius", Number(newValue))
                      }
                      sx={{ flex: 1 }}
                    />
                  </Box>
                </Stack>
              </Box>
            )}
            <BaseSetting />
          </Box>
        )}

        {activeTab === 1 && (
          <Box>
            <FilterControl element={selectedElement} />

            {/* GIF信息 */}
            <Accordion sx={{ mt: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle2">{t("gif_info")}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                  <Box
                    sx={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      {t("animated")}:
                    </Typography>
                    <Typography variant="body2">
                      {selectedElement.properties.isAnimated
                        ? t("yes")
                        : t("no")}
                    </Typography>
                  </Box>
                  {selectedElement.properties.frameCount && (
                    <Box
                      sx={{ display: "flex", justifyContent: "space-between" }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        {t("frame_count")}:
                      </Typography>
                      <Typography variant="body2">
                        {selectedElement.properties.frameCount}
                      </Typography>
                    </Box>
                  )}
                  {selectedElement.properties.frameRate && (
                    <Box
                      sx={{ display: "flex", justifyContent: "space-between" }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        {t("frame_rate")}:
                      </Typography>
                      <Typography variant="body2">
                        {selectedElement.properties.frameRate} fps
                      </Typography>
                    </Box>
                  )}
                </Box>
              </AccordionDetails>
            </Accordion>
          </Box>
        )}
      </Box>
    </Box>
  );
});

export default BasicGif;
